<template>
  <view class="p-4">
    <van-empty :image="image" description="暂无商品">
      <div class="text-sm text-gray-500 mb-4">请选择您要入库的商品</div>
      <div
        class="flex items-center justify-center rounded-full bg-theme-blue px-6 py-2.5 text-sm font-medium text-white shadow-md transition-all duration-300 hover:shadow-lg active:scale-95 active:bg-theme-blue-600"
        @click="handleSelectGoods"
      >
        <i class="i-mdi-package-variant-closed mr-1.5" />
        选择商品
      </div>
    </van-empty>
    <ui-popup v-model:show="showGoodsSelector" title="选择物品" position="bottom" :safe-bottom="false">
      <view class="h-[82vh] flex flex-col bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="px-4 bg-white">
          <uni-search-bar v-model="goodsSearchValue" placeholder="请输入药材名称" @confirm="handleGoodsSearch" @clear="handleGoodsClear" cancelButton="none" />
        </div>
        <div class="flex-1 overflow-hidden">
          <scroll-view scroll-y class="h-full" v-if="goodsListLetter.length > 0" :scroll-top="scrollTop">
            <div class="px-4 pt-2 pr-5 box-border">
              <van-index-bar :index-list="goodsIndexList" :sticky="false">
                <view v-for="item in goodsListLetter" :key="item.letter">
                  <van-index-anchor :index="item.letter" />
                  <div class="grid grid-cols-4 gap-3 my-2">
                    <div
                      v-for="item in item.data"
                      :key="item.id"
                      @click="handleGoodsItemClick(item)"
                      class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col h-16 relative transition-all duration-200"
                      :class="{ 'ring-2 ring-theme-blue bg-blue-50': isSelected(item.id) }"
                    >
                      <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
                      <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
                      <!-- 选中状态指示器 -->
                      <div v-if="isSelected(item.id)" class="absolute -top-1 -right-1 w-5 h-5 bg-theme-blue rounded-full flex items-center justify-center">
                        <i class="i-mdi-check text-white text-xs" />
                      </div>
                    </div>
                  </div>
                </view>
              </van-index-bar>
            </div>
          </scroll-view>
        </div>
        <!-- 底部确认区域 -->
        <div class="px-4 py-3 bg-white border-t border-gray-100 shadow-lg rounded-t-lg" :class="`pb-[env(safe-area-inset-bottom)]`">
          <!-- 已选商品统计 -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <i class="i-mdi-package-variant mr-2 text-theme-blue" />
              <span class="text-sm font-medium text-gray-700">已选商品</span>
            </div>
            <div class="flex items-center">
              <span class="text-lg font-bold text-theme-blue">{{ selectedGoods.length }}</span>
              <span class="text-sm text-gray-500 ml-1">件</span>
            </div>
          </div>

          <!-- 已选商品列表预览 -->
          <div v-if="selectedGoods.length > 0" class="mb-3">
            <div class="flex flex-wrap gap-1 max-h-16 overflow-hidden">
              <div v-for="item in selectedGoods.slice(0, 6)" :key="item.id" class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                {{ item.bzmc }}
              </div>
              <div v-if="selectedGoods.length > 6" class="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">+{{ selectedGoods.length - 6 }}</div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-3">
            <div
              @click="handleClearSelection"
              class="flex-1 py-2.5 px-4 flex items-center justify-center gap-2 tracking-widest bg-gray-100 text-gray-600 rounded-lg text-sm transition-all duration-200 active:bg-gray-200 active:scale-95"
              :disabled="selectedGoods.length === 0"
              :class="{ 'opacity-50 cursor-not-allowed': selectedGoods.length === 0 }"
            >
              <i class="i-mdi-close-circle" />
              <text>清空</text>
            </div>
            <div
              @click="handleConfirmSelection"
              class="flex-1 py-2.5 px-4 flex items-center justify-center gap-2 tracking-widest bg-gradient-to-r from-theme-blue to-blue-500 text-white rounded-lg text-sm shadow-md transition-all duration-200 active:shadow-lg active:scale-95"
              :disabled="selectedGoods.length === 0"
              :class="{ 'opacity-50 cursor-not-allowed': selectedGoods.length === 0 }"
            >
              <i class="i-mdi-check-circle" />
              <text>确认选择</text>
            </div>
          </div>
        </div>
      </view>
    </ui-popup>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicinalItem } from '@/monkey/types';

  // 空状态图片
  const image = ref(monkey.$url.cdn(monkey.$config.empty.noContent));

  // 是否显示商品选择器
  const showGoodsSelector = ref(false);

  // 药材列表
  const goodsList = ref<MedicinalItem[]>([]);

  // 药材列表
  const goodsListLetter = ref<MedicinalItem[]>([]);

  // 索引列表
  const goodsIndexList = ref<string[]>([]);

  // 搜索值
  const goodsSearchValue = ref<string>('');

  // 已选商品列表
  const selectedGoods = ref<MedicinalItem[]>([]);

  // 滚动位置控制
  const scrollTop = ref(0);

  // 定义触发事件
  const emit = defineEmits<{
    (e: 'itemClick', item: MedicinalItem): void;
    (e: 'confirm', items: MedicinalItem[]): void;
  }>();

  /**
   * 检查商品是否已选中
   * @param id 商品ID
   */
  const isSelected = (id: string | number) => {
    return selectedGoods.value.some((item) => item.id === id);
  };

  /**
   * 处理药材项点击事件（多选模式）
   * @param item 药材项
   */
  const handleGoodsItemClick = (item: MedicinalItem) => {
    const index = selectedGoods.value.findIndex((selected) => selected.id === item.id);

    if (index > -1) {
      // 如果已选中，则取消选中
      selectedGoods.value.splice(index, 1);
    } else {
      // 如果未选中，则添加到选中列表
      selectedGoods.value.push(item);
    }

    // 保持原有的单个点击事件
    emit('itemClick', item);
  };

  /**
   * 清空选择
   */
  const handleClearSelection = () => {
    selectedGoods.value = [];
  };

  /**
   * 确认选择
   */
  const handleConfirmSelection = () => {
    if (selectedGoods.value.length > 0) {
      emit('confirm', [...selectedGoods.value]);
      showGoodsSelector.value = false;
      // 清空选择状态
      selectedGoods.value = [];
    }
  };

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        // 将数据转换为MedicinalItem类型
        goodsList.value = data;
        // 将数据转换为索引列表
        goodsListLetter.value = createLetterList(data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 从ywsc字段中提取拼音缩写
      const ywscParts = item.ywsc.split(',');

      // 尝试找到拼音缩写（通常是纯小写字母的部分）
      let pinyin = '';
      for (const part of ywscParts) {
        // 检查是否是拼音缩写（纯小写字母）
        if (/^[a-z]+$/.test(part.trim())) {
          pinyin = part.trim();
          break;
        }
      }

      // 如果没找到拼音，使用药材名称的首字母
      const firstLetter = pinyin.charAt(0).toUpperCase() || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组，并添加提取的拼音
      const enrichedItem = {
        ...item,
        pinyin: pinyin, // 添加提取出的拼音
      };

      groupedData[firstLetter].push(enrichedItem);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    goodsIndexList.value = formattedList.map((item) => item.letter);
    console.log('🚀 ~ createLetterList ~ formattedList:', formattedList);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterGoodsList = (list: MedicinalItem[]) => {
    return list.filter((item) => item.bzmc.includes(goodsSearchValue.value));
  };

  /**
   * 重置滚动位置到顶部
   */
  const resetScrollPosition = () => {
    scrollTop.value = 0;
    // 强制触发滚动更新
    nextTick(() => {
      scrollTop.value = 1;
      nextTick(() => {
        scrollTop.value = 0;
      });
    });
  };

  /**
   * 搜索
   */
  const handleGoodsSearch = () => {
    const result = filterGoodsList(goodsList.value);
    if (result.length > 0) {
      // 过滤列表
      goodsListLetter.value = createLetterList(result);
      // 重置滚动位置
      resetScrollPosition();
    } else monkey.$helper.toast.warning('暂无数据');
  };

  /**
   * 清空搜索
   */
  const handleGoodsClear = () => {
    goodsListLetter.value = createLetterList(goodsList.value);
    // 重置滚动位置
    resetScrollPosition();
  };

  // 处理选择商品事件
  const handleSelectGoods = () => {
    showGoodsSelector.value = true;
    // 打开弹窗时重置滚动位置
    nextTick(() => {
      resetScrollPosition();
    });
  };

  onMounted(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped></style>
